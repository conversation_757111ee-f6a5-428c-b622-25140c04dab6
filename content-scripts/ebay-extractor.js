// Improved eBay-specific product data extractor

(function() {
  'use strict';

  // Ensure CommonExtractor is available
  if (typeof window.CommonExtractor === 'undefined') {
    console.error('CommonExtractor not found! Please inject CommonExtractor first.');
    return;
  }

  const EbayExtractor = {
    // Enhanced eBay-specific selectors
    selectors: {
      title: [
        '#x-title-label-lbl',
        '.x-item-title-label',
        'h1[data-testid="x-item-title-label"]',
        '.notranslate',
        'h1[class*="title"]',
        'h1[id*="title"]'
      ],
      images: [
        '#icImg',
        '.img img',
        '#mainImgHldr img',
        '.ux-image-carousel img',
        '.ux-image-filmstrip img'
      ],
      seller: [
        // eBay seller link patterns (including /sch/ pattern)
        'a[href*="/sch/"][href*="/m.html"]',
        'a[href*="ebay.com/sch/"][href*="/m.html"]',
        'a[href*="/usr/"]',
        'a[href*="ebay.com/usr/"]',
        'a[href*="/str/"]',
        'a[href*="ebay.com/str/"]'
      ],
      description: [
        '#desc_div',
        '.item-description',
        '[data-testid="item-description"]',
        '.item-specifics',
        '.u-flL.condText'
      ]
    },

    // Extract product data from eBay listing page
    async extractProductData() {
      try {
        CommonExtractor.log('Starting eBay product data extraction...');

        // Wait a bit for dynamic content to load
        await this.waitForContent();

        const productData = {
          title: this.extractTitle(),
          productUrl: CommonExtractor.getCurrentUrl(),
          marketplace: 'ebay',
          sellerName: this.extractSeller(),
          images: this.extractImages(),
          metadata: this.extractMetadata()
        };

        CommonExtractor.log('Extracted product data:', productData);

        // Validate the extracted data
        const validation = CommonExtractor.validateProductData(productData);

        if (!validation.isValid) {
          CommonExtractor.log('Validation failed:', validation.errors);
          CommonExtractor.showNotification(
            'Failed to extract complete product data: ' + validation.errors.join(', '),
            'error'
          );
          return null;
        }

        CommonExtractor.log('eBay product data extracted successfully');
        CommonExtractor.showNotification('Product data extracted successfully!', 'success');
        return productData;

      } catch (error) {
        CommonExtractor.log('Error extracting eBay product data:', error);
        CommonExtractor.showNotification('Error extracting product data: ' + error.message, 'error');
        return null;
      }
    },

    // Wait for content to load
    async waitForContent() {
      try {
        // Wait for either title or images to be available
        await Promise.race([
          CommonExtractor.waitForElement(this.selectors.title[0], 3000),
          CommonExtractor.waitForElement(this.selectors.images[0], 3000)
        ]);

        // Additional wait for dynamic content
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        CommonExtractor.log('Content wait timeout, proceeding anyway');
      }
    },

    extractTitle() {
      CommonExtractor.log('Extracting title...');

      for (const selector of this.selectors.title) {
        const title = CommonExtractor.extractText(selector);
        if (title && title.length > 3) {
          CommonExtractor.log(`Title found with selector: ${selector}`);
          return CommonExtractor.cleanText(title);
        }
      }

      // Fallback to page title
      const pageTitle = document.title;
      if (pageTitle && !pageTitle.toLowerCase().includes('ebay') && pageTitle.length > 3) {
        const cleanTitle = pageTitle.split('|')[0].split('-')[0].trim();
        if (cleanTitle.length > 3) {
          CommonExtractor.log('Using page title as fallback');
          return CommonExtractor.cleanText(cleanTitle);
        }
      }

      // Last resort - try any h1
      const h1Elements = document.querySelectorAll('h1');
      for (const h1 of h1Elements) {
        const text = CommonExtractor.cleanText(h1.textContent);
        if (text && text.length > 3 && !text.toLowerCase().includes('ebay')) {
          CommonExtractor.log('Using h1 as last resort');
          return text;
        }
      }

      throw new Error('Could not extract product title');
    },

    extractSeller() {
      CommonExtractor.log('Extracting seller...');

      for (const selector of this.selectors.seller) {
        try {
          const element = document.querySelector(selector);
          if (element) {
            let seller = CommonExtractor.extractText(selector);

            if (seller && seller.length > 1) {
              seller = CommonExtractor.cleanText(seller);
              CommonExtractor.log(`Seller found with selector "${selector}": ${seller}`);
              return seller;
            }
          }
        } catch (error) {
          CommonExtractor.log(`Error with selector "${selector}": ${error.message}`);
        }
      }

      CommonExtractor.log('No seller found');
      return null;
    },

    extractImages() {
      CommonExtractor.log('Extracting images...');

      const images = CommonExtractor.extractImages(this.selectors.images);

      // eBay-specific image processing
      const processedImages = images.map((image, index) => {
        let imageUrl = image.imageUrl;

        // Convert eBay thumbnail URLs to full size
        if (imageUrl.includes('s-l64')) {
          imageUrl = imageUrl.replace('s-l64', 's-l1600');
        } else if (imageUrl.includes('s-l225')) {
          imageUrl = imageUrl.replace('s-l225', 's-l1600');
        } else if (imageUrl.includes('s-l300')) {
          imageUrl = imageUrl.replace('s-l300', 's-l1600');
        } else if (imageUrl.includes('s-l500')) {
          imageUrl = imageUrl.replace('s-l500', 's-l1600');
        } else if (imageUrl.includes('s-l140')) {
          imageUrl = imageUrl.replace('s-l140', 's-l1600');
        }

        return {
          ...image,
          imageUrl: imageUrl,
          isPrimary: index === 0,
          sortOrder: index
        };
      });

      CommonExtractor.log(`Found ${processedImages.length} images`);
      return processedImages;
    },

    extractMetadata() {
      CommonExtractor.log('Extracting metadata...');

      const metadata = {};

      // Extract description
      const description = this.extractDescription();
      if (description) {
        metadata.description = description;
      }

      CommonExtractor.log('Extracted metadata:', metadata);
      return metadata;
    },

    extractDescription() {
      for (const selector of this.selectors.description) {
        const description = CommonExtractor.extractText(selector);
        if (description && description.length > 10) {
          return CommonExtractor.cleanText(description).substring(0, 500);
        }
      }

      return null;
    },

    // Check if current page is an eBay product page
    isProductPage() {
      return (window.location.pathname.includes('/itm/') ||
              window.location.pathname.includes('/p/')) &&
             window.location.hostname.includes('ebay.com');
    },

    // Manual extraction trigger
    async extract() {
      if (!this.isProductPage()) {
        CommonExtractor.showNotification('This is not an eBay product page!', 'error');
        return null;
      }

      CommonExtractor.showNotification('Extracting product data...', 'info');
      return await this.extractProductData();
    }
  };

  // Make it globally available
  window.EbayExtractor = EbayExtractor;

  // Auto-extract when page loads (for manual crawling)
  if (EbayExtractor.isProductPage()) {
    // Wait for page to fully load
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
          CommonExtractor.log('eBay product page detected and ready for extraction');
        }, 1000);
      });
    } else {
      CommonExtractor.log('eBay product page detected and ready for extraction');
    }
  }

  console.log('Improved EbayExtractor loaded successfully!');
})();
