// Content script for syncing authentication state with the web application
// This script runs on the web application pages to access NextAuth session data

class WebAppAuthSync {
  constructor() {
    this.init();
  }

  init() {
    // Listen for messages from the extension
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep message channel open for async responses
    });

    console.log('WebApp Auth Sync content script loaded');
  }

  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.type) {
        case 'GET_WEB_APP_AUTH_STATUS':
          await this.getWebAppAuthStatus(sendResponse);
          break;

        case 'SYNC_AUTH_TOKEN':
          await this.syncAuthToken(message.token, sendResponse);
          break;

        default:
          sendResponse({ success: false, error: 'Unknown message type' });
      }
    } catch (error) {
      console.error('Error handling message in webapp auth sync:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async getWebAppAuthStatus(sendResponse) {
    try {
      // Try to get NextAuth session data
      const authStatus = await this.getNextAuthSession();

      if (authStatus.isAuthenticated) {
        sendResponse({
          isAuthenticated: true,
          user: authStatus.user,
          accessToken: authStatus.accessToken,
          source: 'nextauth'
        });
      } else {
        sendResponse({ isAuthenticated: false });
      }
    } catch (error) {
      console.error('Error getting web app auth status:', error);
      sendResponse({ isAuthenticated: false, error: error.message });
    }
  }

  async getNextAuthSession() {
    try {
      // Method 1: Try to access NextAuth session via API
      const sessionResponse = await fetch('/api/auth/session', {
        credentials: 'include'
      });

      if (sessionResponse.ok) {
        const sessionData = await sessionResponse.json();
        console.log('🔍 [WebApp Auth Sync] Full session data:', sessionData);

        if (sessionData && sessionData.user) {
          // Based on your session structure, the JWT token is in 'backendToken'
          // Let's check multiple possible locations for the access token
          const accessToken = sessionData.backendToken ||
                             sessionData.accessToken ||
                             sessionData.access_token ||
                             sessionData.token ||
                             sessionData.user.accessToken ||
                             sessionData.user.access_token;

          console.log('🔍 [WebApp Auth Sync] Session data keys:', Object.keys(sessionData));
          console.log('🔍 [WebApp Auth Sync] Found access token:', accessToken ? 'YES' : 'NO');
          console.log('🔍 [WebApp Auth Sync] Token source:',
            sessionData.backendToken ? 'backendToken' :
            sessionData.accessToken ? 'accessToken' :
            sessionData.access_token ? 'access_token' :
            sessionData.token ? 'token' :
            sessionData.user?.accessToken ? 'user.accessToken' :
            sessionData.user?.access_token ? 'user.access_token' : 'NONE');

          return {
            isAuthenticated: true,
            user: {
              id: sessionData.user.id,
              email: sessionData.user.email,
              name: sessionData.user.name,
              role: sessionData.user.role
            },
            accessToken: accessToken
          };
        }
      }

      // Method 2: Try to access session from window object (if available)
      if (window.__NEXT_DATA__ && window.__NEXT_DATA__.props && window.__NEXT_DATA__.props.pageProps) {
        const pageProps = window.__NEXT_DATA__.props.pageProps;
        if (pageProps.session && pageProps.session.user) {
          console.log('🔍 [WebApp Auth Sync] Found session in __NEXT_DATA__:', pageProps.session);

          const accessToken = pageProps.session.backendToken ||
                             pageProps.session.accessToken ||
                             pageProps.session.access_token ||
                             pageProps.session.token;

          return {
            isAuthenticated: true,
            user: pageProps.session.user,
            accessToken: accessToken
          };
        }
      }

      // Method 3: Try to get access token from a custom endpoint
      try {
        const tokenResponse = await fetch('/api/auth/token', {
          credentials: 'include'
        });

        if (tokenResponse.ok) {
          const tokenData = await tokenResponse.json();
          console.log('🔍 [WebApp Auth Sync] Token endpoint response:', tokenData);

          if (tokenData.accessToken) {
            // We have a token, but we still need user info from the session
            const sessionResponse2 = await fetch('/api/auth/session', {
              credentials: 'include'
            });

            if (sessionResponse2.ok) {
              const sessionData2 = await sessionResponse2.json();
              if (sessionData2 && sessionData2.user) {
                return {
                  isAuthenticated: true,
                  user: {
                    id: sessionData2.user.id,
                    email: sessionData2.user.email,
                    name: sessionData2.user.name
                  },
                  accessToken: tokenData.accessToken
                };
              }
            }
          }
        }
      } catch (tokenError) {
        console.log('🔍 [WebApp Auth Sync] Token endpoint not available:', tokenError.message);
      }

      // Method 4: Try to access from localStorage/sessionStorage (if the app stores it there)
      const storedSession = localStorage.getItem('nextauth.session-token') ||
                           sessionStorage.getItem('nextauth.session-token');

      if (storedSession) {
        console.log('🔍 [WebApp Auth Sync] Found stored session token');
        // This would need to be decoded/verified, but for now we'll just indicate there's a session
        return {
          isAuthenticated: true,
          user: { email: 'unknown' }, // We can't decode the session token here
          accessToken: null
        };
      }

      console.log('❌ [WebApp Auth Sync] No authentication found');
      return { isAuthenticated: false };
    } catch (error) {
      console.error('Error getting NextAuth session:', error);
      return { isAuthenticated: false };
    }
  }

  async syncAuthToken(token, sendResponse) {
    try {
      // This method could be used to sync tokens from extension to web app
      // For now, we'll just acknowledge the sync
      sendResponse({ success: true, message: 'Token sync acknowledged' });
    } catch (error) {
      console.error('Error syncing auth token:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  // Method to detect if user is authenticated by checking DOM elements
  detectAuthFromDOM() {
    try {
      // Look for common authentication indicators in the DOM
      const userMenus = document.querySelectorAll('[data-testid="user-menu"], .user-menu, .profile-menu');
      const logoutButtons = document.querySelectorAll('[data-testid="logout"], button[title*="logout"], button[title*="sign out"]');
      const userAvatars = document.querySelectorAll('.user-avatar, .profile-avatar, [data-testid="user-avatar"]');

      // If we find user-specific UI elements, user is likely authenticated
      if (userMenus.length > 0 || logoutButtons.length > 0 || userAvatars.length > 0) {
        return { isAuthenticated: true, source: 'dom-detection' };
      }

      // Look for login/signin buttons (indicates user is NOT authenticated)
      const loginButtons = document.querySelectorAll('[data-testid="login"], button[title*="login"], button[title*="sign in"], a[href*="signin"]');
      if (loginButtons.length > 0) {
        return { isAuthenticated: false, source: 'dom-detection' };
      }

      return { isAuthenticated: false, source: 'dom-detection-inconclusive' };
    } catch (error) {
      console.error('Error detecting auth from DOM:', error);
      return { isAuthenticated: false, source: 'dom-detection-error' };
    }
  }
}

// Initialize the auth sync when the script loads
new WebAppAuthSync();
