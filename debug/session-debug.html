<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NextAuth Session Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        pre {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #005a87;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>NextAuth Session Debug Tool</h1>
        <p>This tool helps debug the NextAuth session structure for Chrome extension integration.</p>

        <div class="section">
            <h3>Instructions</h3>
            <ol>
                <li>Make sure you're logged in to the web application</li>
                <li><strong>IMPORTANT:</strong> Copy this file to your web application's public folder (e.g., <code>public/debug-session.html</code>)</li>
                <li>Access it via <code>http://localhost:3000/debug-session.html</code> to avoid CORS issues</li>
                <li>Click the buttons below to test different session access methods</li>
                <li>Check the console for detailed logs</li>
            </ol>
            <div class="status info">
                <strong>Note:</strong> If you're seeing "Failed to fetch" errors, make sure you're accessing this file through the web server (http://localhost:3000) and not as a local file (file://).
            </div>
        </div>

        <div class="section">
            <h3>Session Tests</h3>
            <button onclick="testSessionAPI()">Test /api/auth/session</button>
            <button onclick="testTokenAPI()">Test /api/auth/token</button>
            <button onclick="testWindowData()">Test window.__NEXT_DATA__</button>
            <button onclick="testLocalStorage()">Test localStorage</button>
            <button onclick="testAllMethods()">Test All Methods</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        function addResult(title, data, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const section = document.createElement('div');
            section.className = 'section';

            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>${title}</strong>`;

            const preElement = document.createElement('pre');
            preElement.textContent = typeof data === 'object' ? JSON.stringify(data, null, 2) : data;

            section.appendChild(statusDiv);
            section.appendChild(preElement);
            resultsDiv.appendChild(section);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testSessionAPI() {
            console.log('🔍 Testing /api/auth/session endpoint...');
            clearResults();

            try {
                const response = await fetch('/api/auth/session', {
                    credentials: 'include'
                });

                if (response.ok) {
                    const sessionData = await response.json();
                    console.log('✅ Session API response:', sessionData);
                    addResult('✅ /api/auth/session Success', sessionData, 'success');

                    // Check for access token in various locations
                    const tokenLocations = {
                        'sessionData.backendToken': sessionData.backendToken,
                        'sessionData.accessToken': sessionData.accessToken,
                        'sessionData.access_token': sessionData.access_token,
                        'sessionData.token': sessionData.token,
                        'sessionData.user.accessToken': sessionData.user?.accessToken,
                        'sessionData.user.access_token': sessionData.user?.access_token,
                        'sessionData.refreshToken': sessionData.refreshToken,
                        'sessionData.backendTokenExpiry': sessionData.backendTokenExpiry
                    };

                    addResult('🔑 Token Location Analysis', tokenLocations, 'info');

                    // Specifically highlight the backend token if found
                    if (sessionData.backendToken) {
                        addResult('✅ Backend Token Found!', {
                            token: sessionData.backendToken,
                            expiry: sessionData.backendTokenExpiry,
                            expiryDate: sessionData.backendTokenExpiry ? new Date(sessionData.backendTokenExpiry).toISOString() : 'Unknown'
                        }, 'success');
                    }
                } else {
                    const errorText = await response.text();
                    console.error('❌ Session API failed:', response.status, errorText);
                    addResult('❌ /api/auth/session Failed', `Status: ${response.status}\n${errorText}`, 'error');
                }
            } catch (error) {
                console.error('❌ Session API error:', error);
                addResult('❌ /api/auth/session Error', error.message, 'error');
            }
        }

        async function testTokenAPI() {
            console.log('🔍 Testing /api/auth/token endpoint...');

            try {
                const response = await fetch('/api/auth/token', {
                    credentials: 'include'
                });

                if (response.ok) {
                    const tokenData = await response.json();
                    console.log('✅ Token API response:', tokenData);
                    addResult('✅ /api/auth/token Success', tokenData, 'success');
                } else {
                    const errorText = await response.text();
                    console.error('❌ Token API failed:', response.status, errorText);
                    addResult('❌ /api/auth/token Failed', `Status: ${response.status}\n${errorText}`, 'error');
                }
            } catch (error) {
                console.error('❌ Token API error:', error);
                addResult('❌ /api/auth/token Error', error.message, 'error');
            }
        }

        function testWindowData() {
            console.log('🔍 Testing window.__NEXT_DATA__...');

            if (window.__NEXT_DATA__) {
                console.log('✅ Found __NEXT_DATA__:', window.__NEXT_DATA__);
                addResult('✅ window.__NEXT_DATA__ Found', window.__NEXT_DATA__, 'success');

                if (window.__NEXT_DATA__.props?.pageProps?.session) {
                    addResult('✅ Session in pageProps', window.__NEXT_DATA__.props.pageProps.session, 'success');
                } else {
                    addResult('❌ No session in pageProps', 'Session not found in window.__NEXT_DATA__.props.pageProps', 'error');
                }
            } else {
                console.log('❌ __NEXT_DATA__ not found');
                addResult('❌ window.__NEXT_DATA__ Not Found', 'window.__NEXT_DATA__ is not available', 'error');
            }
        }

        function testLocalStorage() {
            console.log('🔍 Testing localStorage...');

            const storageKeys = [
                'nextauth.session-token',
                'next-auth.session-token',
                'nextauth.csrf-token',
                'next-auth.csrf-token'
            ];

            const storageData = {};

            storageKeys.forEach(key => {
                const value = localStorage.getItem(key);
                if (value) {
                    storageData[key] = value;
                }
            });

            if (Object.keys(storageData).length > 0) {
                console.log('✅ Found localStorage data:', storageData);
                addResult('✅ localStorage Data Found', storageData, 'success');
            } else {
                console.log('❌ No relevant localStorage data found');
                addResult('❌ localStorage Data Not Found', 'No NextAuth tokens found in localStorage', 'error');
            }
        }

        async function testAllMethods() {
            clearResults();
            addResult('🚀 Running All Tests', 'Testing all authentication methods...', 'info');

            await testSessionAPI();
            await testTokenAPI();
            testWindowData();
            testLocalStorage();

            addResult('✅ All Tests Complete', 'Check the results above and browser console for details', 'success');
        }

        // Auto-run basic test when page loads
        window.addEventListener('load', () => {
            console.log('🔍 NextAuth Session Debug Tool loaded');
            console.log('🔍 Current URL:', window.location.href);
            console.log('🔍 User Agent:', navigator.userAgent);
        });
    </script>
</body>
</html>
