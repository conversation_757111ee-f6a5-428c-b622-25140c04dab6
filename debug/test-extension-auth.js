// Test script to verify Chrome extension authentication
// Run this in the browser console on a web app page where you're logged in

console.log('🔍 Testing Chrome Extension Authentication...');

// Test 1: Check if the webapp-auth-sync content script is loaded
console.log('\n📋 Test 1: Content Script Detection');
if (typeof chrome !== 'undefined' && chrome.runtime) {
    console.log('✅ Chrome extension APIs available');
} else {
    console.log('❌ Chrome extension APIs not available');
}

// Test 2: Simulate the extension's auth status request
console.log('\n📋 Test 2: Simulating Extension Auth Request');

async function testAuthSync() {
    try {
        // This simulates what the extension does
        const sessionResponse = await fetch('/api/auth/session', {
            credentials: 'include'
        });
        
        if (sessionResponse.ok) {
            const sessionData = await sessionResponse.json();
            console.log('✅ Session data retrieved:', sessionData);
            
            // Check for backend token (this is what the extension needs)
            if (sessionData.backendToken) {
                console.log('✅ Backend token found:', sessionData.backendToken.substring(0, 20) + '...');
                console.log('✅ Token expiry:', new Date(sessionData.backendTokenExpiry).toISOString());
                
                // Test if the token is valid by making a test request
                console.log('\n📋 Test 3: Testing Token Validity');
                const testResponse = await fetch('http://localhost:3001/auth/profile', {
                    headers: {
                        'Authorization': `Bearer ${sessionData.backendToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (testResponse.ok) {
                    const profileData = await testResponse.json();
                    console.log('✅ Token is valid! Profile data:', profileData);
                } else {
                    console.log('❌ Token validation failed:', testResponse.status, await testResponse.text());
                }
                
            } else {
                console.log('❌ No backend token found in session');
                console.log('Available session keys:', Object.keys(sessionData));
            }
        } else {
            console.log('❌ Failed to get session:', sessionResponse.status);
        }
    } catch (error) {
        console.error('❌ Error testing auth sync:', error);
    }
}

// Test 3: Simulate the extension message handler
console.log('\n📋 Test 4: Simulating Extension Message Handler');

function simulateExtensionMessage() {
    // This simulates the message that the extension sends to the content script
    const message = {
        type: 'GET_WEB_APP_AUTH_STATUS'
    };
    
    console.log('📡 Simulating extension message:', message);
    
    // If the webapp-auth-sync content script is working, it should respond to this
    // In a real scenario, this would be handled by chrome.runtime.onMessage
    console.log('ℹ️ In the real extension, this message would be handled by the webapp-auth-sync.js content script');
}

// Run the tests
testAuthSync();
simulateExtensionMessage();

// Test 4: Check for common authentication indicators
console.log('\n📋 Test 5: DOM Authentication Indicators');

const authIndicators = {
    userMenus: document.querySelectorAll('[data-testid="user-menu"], .user-menu, .profile-menu').length,
    logoutButtons: document.querySelectorAll('[data-testid="logout"], button[title*="logout"], button[title*="sign out"]').length,
    userAvatars: document.querySelectorAll('.user-avatar, .profile-avatar, [data-testid="user-avatar"]').length,
    loginButtons: document.querySelectorAll('[data-testid="login"], button[title*="login"], button[title*="sign in"], a[href*="signin"]').length
};

console.log('🔍 DOM Authentication Indicators:', authIndicators);

if (authIndicators.userMenus > 0 || authIndicators.logoutButtons > 0 || authIndicators.userAvatars > 0) {
    console.log('✅ User appears to be authenticated (found user UI elements)');
} else if (authIndicators.loginButtons > 0) {
    console.log('❌ User appears to be NOT authenticated (found login buttons)');
} else {
    console.log('⚠️ Authentication status unclear from DOM');
}

console.log('\n🎯 Summary:');
console.log('1. Run this script in the web app console while logged in');
console.log('2. Check that the backend token is found and valid');
console.log('3. If successful, the Chrome extension should now work');
console.log('4. Try extracting a product from Etsy to test the full flow');
