{
	"folders": [
		{
			"name": "📁 Main Project",
			"path": "."
		},
		{
			"name": "🔧 Backend (NestJS)",
			"path": "./tts-be-nestjs"
		},
		{
			"name": "🎨 Frontend (Next.js)",
			"path": "./tts-fe-nextjs"
		},
		{
			"name": "🔌 Chrome Extension",
			"path": "../../../mnt/c/Users/<USER>/CascadeProjects/tiktokshop/tts-chrome-extension"
		}
	],
	"settings": {
		"git.detectSubmodules": true,
		"git.enableSmartCommit": true,
		"git.confirmSync": false
	},
	"extensions": {
		"recommendations": [
			"ms-vscode.vscode-typescript-next",
			"bradlc.vscode-tailwindcss",
			"esbenp.prettier-vscode",
			"ms-vscode.vscode-json",
		]
	}
}